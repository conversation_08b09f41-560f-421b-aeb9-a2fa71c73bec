import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'


// Define protected routes that require authentication
const isProtectedRoute = createRouteMatcher([
    '/dashboard(.*)',
    '/admin(.*)',
    '/profile(.*)',
    '/submit-art(.*)',
    '/artist-submissions(.*)',
    '/my-submissions(.*)'
])

export default clerkMiddleware(async (auth, req) => {
    // Protect all defined routes using auth.protect() for automatic redirect handling
    if (isProtectedRoute(req)) {
        await auth.protect()
    }
})

export const config = {
    matcher: [
        // Skip Next.js internals and all static files, unless found in search params
        '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
        // Always run for API routes
        '/(api|trpc)(.*)',
    ],
}