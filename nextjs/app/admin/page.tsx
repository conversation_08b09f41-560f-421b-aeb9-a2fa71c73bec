import { currentUser } from '@clerk/nextjs/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { checkRole } from '@/utils/roles' //added following Clerk documentation


export default async function AdminPage() {
  const user = await currentUser()

  if (!user) {
    redirect('/sign-in')
  }

  const isAdmin = await checkRole('admin')

  if (!isAdmin) {
    redirect('/')
  }

  // In a real app, you would check if the user has admin role
  // For now, we'll show a placeholder admin dashboard

  return (
    <main id="main-content" className="min-h-screen">
      <div className="container">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <section className="hero-section">
            <div className="hero-content">
              <h1 className="hero-title">Admin Dashboard</h1>
              <p className="hero-subtitle">
                Manage Chocolate & Art Show events, artists, and submissions
              </p>
            </div>
          </section>

          {/* Admin Stats */}
          <section className="cities-grid">
            <article className="city-card">
              <div className="city-card__header">
                <h2 className="city-card__title">Total Submissions</h2>
                <span className="city-card__status">47</span>
              </div>
              <div className="city-card__details">
                <div className="city-card__detail">
                  Artist applications received for upcoming shows
                </div>
              </div>
            </article>

            <article className="city-card">
              <div className="city-card__header">
                <h2 className="city-card__title">Approved Artists</h2>
                <span className="city-card__status bg-success">23</span>
              </div>
              <div className="city-card__details">
                <div className="city-card__detail">
                  Artists confirmed for the Dallas show
                </div>
              </div>
            </article>

            <article className="city-card">
              <div className="city-card__header">
                <h2 className="city-card__title">Pending Review</h2>
                <span className="city-card__status bg-warning">12</span>
              </div>
              <div className="city-card__details">
                <div className="city-card__detail">
                  Submissions awaiting admin review
                </div>
              </div>
            </article>

            <article className="city-card">
              <div className="city-card__header">
                <h2 className="city-card__title">Registered Users</h2>
                <span className="city-card__status bg-info">156</span>
              </div>
              <div className="city-card__details">
                <div className="city-card__detail">
                  Total users registered on the platform
                </div>
              </div>
            </article>
          </section>

          {/* Admin Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {/* Manage Submissions */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Artist Submissions</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Review and manage artist applications and submissions
              </p>
              <Link
                href="/admin/submissions"
                className="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-300"
              >
                Manage Submissions
              </Link>
            </div>

            {/* Manage Events */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Events</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Create and manage upcoming shows and events
              </p>
              <Link
                href="/admin/events"
                className="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-300"
              >
                Manage Events
              </Link>
            </div>

            {/* User Management */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Users</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Manage user accounts and permissions
              </p>
              <Link
                href="/admin/users"
                className="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-300"
              >
                Manage Users
              </Link>
            </div>

            {/* Gallery Management */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Gallery</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Manage gallery images and albums
              </p>
              <Link
                href="/admin/gallery"
                className="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-300"
              >
                Manage Gallery
              </Link>
            </div>

            {/* Content Management */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Content</h3>
              </div>
              <p className="text-gray-300 mb-4">
                Edit website content and pages
              </p>
              <Link
                href="/admin/content"
                className="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-300"
              >
                Manage Content
              </Link>
            </div>

            {/* Analytics */}
            <div className="bg-gray-900 border-2 border-gray-700 rounded-lg p-6 hover:border-pink-500 transition-colors duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-pink-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white ml-4">Analytics</h3>
              </div>
              <p className="text-gray-300 mb-4">
                View site analytics and reports
              </p>
              <Link
                href="/admin/analytics"
                className="inline-block bg-pink-500 hover:bg-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-300"
              >
                View Analytics
              </Link>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-gray-900 border border-gray-700 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Recent Activity</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-3 border-b border-gray-700">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-white">New artist submission approved</p>
                    <p className="text-gray-400 text-sm">Sarah Johnson - Digital Art</p>
                  </div>
                </div>
                <span className="text-gray-400 text-sm">2 hours ago</span>
              </div>

              <div className="flex items-center justify-between py-3 border-b border-gray-700">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-white">New user registration</p>
                    <p className="text-gray-400 text-sm"><EMAIL></p>
                  </div>
                </div>
                <span className="text-gray-400 text-sm">4 hours ago</span>
              </div>

              <div className="flex items-center justify-between py-3">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-white">New submission pending review</p>
                    <p className="text-gray-400 text-sm">Alex Chen - Sculpture</p>
                  </div>
                </div>
                <span className="text-gray-400 text-sm">6 hours ago</span>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-8">
            <Link
              href="/dashboard"
              className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:text-white hover:border-gray-500 transition-colors duration-300"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </main>
  )
}
